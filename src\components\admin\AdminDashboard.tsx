import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Users,
  ShoppingCart,
  Package,
  DollarSign,
  TrendingUp,
  Eye,
  Star,
  BarChart3,
  AlertCircle,
  Calendar,
  Download
} from 'lucide-react';

interface DashboardStats {
  totalUsers: number;
  totalOrders: number;
  totalProducts: number;
  totalRevenue: number;
  recentOrders: any[];
  popularProducts: any[];
  salesTrends: {
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  customerInsights: {
    newCustomers: number;
    returningCustomers: number;
    averageOrderValue: number;
  };
  inventoryAlerts: {
    lowStock: any[];
    outOfStock: any[];
  };
  monthlyRevenue: { month: string; revenue: number }[];
  orderStatusBreakdown: { status: string; count: number; color: string }[];
  topCategories: { category: string; revenue: number; orders: number }[];
  dailyOrders: { date: string; orders: number; revenue: number }[];
}

export const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalOrders: 0,
    totalProducts: 0,
    totalRevenue: 0,
    recentOrders: [],
    popularProducts: [],
    salesTrends: {
      thisMonth: 0,
      lastMonth: 0,
      growth: 0
    },
    customerInsights: {
      newCustomers: 0,
      returningCustomers: 0,
      averageOrderValue: 0
    },
    inventoryAlerts: {
      lowStock: [],
      outOfStock: []
    },
    monthlyRevenue: [],
    orderStatusBreakdown: [],
    topCategories: [],
    dailyOrders: []
  });
  const [loading, setLoading] = useState(true);
  const [timePeriod, setTimePeriod] = useState('30'); // days
  const [autoRefresh, setAutoRefresh] = useState(false);

  useEffect(() => {
    fetchDashboardStats();
  }, [timePeriod]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchDashboardStats();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, timePeriod]);

  const fetchDashboardStats = async () => {
    try {
      const now = new Date();
      const daysAgo = parseInt(timePeriod);
      const startDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

      // Fetch total users
      const { count: userCount } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true });

      // Fetch total orders
      const { count: orderCount } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true });

      // Fetch total products
      const { count: productCount } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });

      // Fetch total revenue
      const { data: revenueData } = await supabase
        .from('orders')
        .select('total_amount, created_at')
        .eq('payment_status', 'completed');

      const totalRevenue = revenueData?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;

      // Sales trends
      const thisMonthRevenue = revenueData?.filter(order =>
        new Date(order.created_at) >= thisMonth
      ).reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;

      const lastMonthRevenue = revenueData?.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= lastMonth && orderDate <= lastMonthEnd;
      }).reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;

      const growth = lastMonthRevenue > 0 ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

      // Fetch recent orders
      const { data: recentOrders } = await supabase
        .from('orders')
        .select(`
          *,
          user_profiles(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      // Fetch popular products
      const { data: popularProducts } = await supabase
        .from('products')
        .select('*')
        .eq('featured', true)
        .limit(5);

      // Customer insights
      const { data: newCustomers } = await supabase
        .from('user_profiles')
        .select('id')
        .gte('created_at', thisMonth.toISOString());

      const { data: allOrders } = await supabase
        .from('orders')
        .select('user_id, total_amount')
        .eq('payment_status', 'completed');

      const uniqueCustomers = new Set(allOrders?.map(o => o.user_id)).size;
      const returningCustomers = uniqueCustomers - (newCustomers?.length || 0);
      const averageOrderValue = allOrders?.length ? totalRevenue / allOrders.length : 0;

      // Inventory alerts
      const { data: lowStockProducts } = await supabase
        .from('products')
        .select('*')
        .lt('stock_quantity', 10)
        .gt('stock_quantity', 0);

      const { data: outOfStockProducts } = await supabase
        .from('products')
        .select('*')
        .eq('stock_quantity', 0);

      // Monthly revenue for the last 6 months
      const monthlyRevenue = [];
      for (let i = 5; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

        const monthRevenue = revenueData?.filter(order => {
          const orderDate = new Date(order.created_at);
          return orderDate >= monthStart && orderDate <= monthEnd;
        }).reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;

        monthlyRevenue.push({
          month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: monthRevenue
        });
      }

      // Order status breakdown
      const statusColors = {
        pending: '#f59e0b',
        confirmed: '#3b82f6',
        processing: '#8b5cf6',
        shipped: '#10b981',
        delivered: '#059669',
        cancelled: '#ef4444',
        refunded: '#6b7280'
      };

      const { data: ordersByStatus } = await supabase
        .from('orders')
        .select('status')
        .gte('created_at', startDate.toISOString());

      const statusCounts = ordersByStatus?.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      const orderStatusBreakdown = Object.entries(statusCounts).map(([status, count]) => ({
        status: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        color: statusColors[status as keyof typeof statusColors] || '#6b7280'
      }));

      // Top categories by revenue
      const { data: categoryData } = await supabase
        .from('order_items')
        .select(`
          quantity,
          unit_price,
          products(category)
        `)
        .gte('created_at', startDate.toISOString());

      const categoryRevenue = categoryData?.reduce((acc, item) => {
        const category = item.products?.category || 'Uncategorized';
        const revenue = item.quantity * item.unit_price;
        if (!acc[category]) {
          acc[category] = { revenue: 0, orders: 0 };
        }
        acc[category].revenue += revenue;
        acc[category].orders += 1;
        return acc;
      }, {} as Record<string, { revenue: number; orders: number }>) || {};

      const topCategories = Object.entries(categoryRevenue)
        .map(([category, data]) => ({
          category,
          revenue: data.revenue,
          orders: data.orders
        }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5);

      // Daily orders for the last 30 days
      const dailyOrders = [];
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

        const dayOrders = revenueData?.filter(order => {
          const orderDate = new Date(order.created_at);
          return orderDate >= dayStart && orderDate < dayEnd;
        }) || [];

        dailyOrders.push({
          date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          orders: dayOrders.length,
          revenue: dayOrders.reduce((sum, order) => sum + Number(order.total_amount), 0)
        });
      }

      setStats({
        totalUsers: userCount || 0,
        totalOrders: orderCount || 0,
        totalProducts: productCount || 0,
        totalRevenue,
        recentOrders: recentOrders || [],
        popularProducts: popularProducts || [],
        salesTrends: {
          thisMonth: thisMonthRevenue,
          lastMonth: lastMonthRevenue,
          growth
        },
        customerInsights: {
          newCustomers: newCustomers?.length || 0,
          returningCustomers,
          averageOrderValue
        },
        inventoryAlerts: {
          lowStock: lowStockProducts || [],
          outOfStock: outOfStockProducts || []
        },
        monthlyRevenue,
        orderStatusBreakdown,
        topCategories,
        dailyOrders
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    const csvData = [
      ['Metric', 'Value'],
      ['Total Users', stats.totalUsers],
      ['Total Orders', stats.totalOrders],
      ['Total Products', stats.totalProducts],
      ['Total Revenue', `$${stats.totalRevenue.toFixed(2)}`],
      ['This Month Revenue', `$${stats.salesTrends.thisMonth.toFixed(2)}`],
      ['Last Month Revenue', `$${stats.salesTrends.lastMonth.toFixed(2)}`],
      ['Growth Rate', `${stats.salesTrends.growth.toFixed(1)}%`],
      ['Average Order Value', `$${stats.customerInsights.averageOrderValue.toFixed(2)}`],
      ['New Customers', stats.customerInsights.newCustomers],
      ['Returning Customers', stats.customerInsights.returningCustomers],
      ['', ''],
      ['Monthly Revenue Breakdown', ''],
      ...stats.monthlyRevenue.map(item => [item.month, `$${item.revenue.toFixed(2)}`]),
      ['', ''],
      ['Order Status Breakdown', ''],
      ...stats.orderStatusBreakdown.map(item => [item.status, item.count]),
      ['', ''],
      ['Top Categories', ''],
      ...stats.topCategories.map(item => [item.category, `$${item.revenue.toFixed(2)}`])
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dashboard-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return <div className="text-center py-8">Loading dashboard...</div>;
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Total Orders',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Total Revenue',
      value: `$${stats.totalRevenue.toFixed(2)}`,
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">
            Overview of your Depths of Perception business
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={timePeriod} onValueChange={setTimePeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
          </Button>
          <Button variant="outline" onClick={exportToCSV}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Sales Trends */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.salesTrends.thisMonth.toFixed(2)}</div>
            <p className={`text-xs ${stats.salesTrends.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {stats.salesTrends.growth >= 0 ? '+' : ''}{stats.salesTrends.growth.toFixed(1)}% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.customerInsights.averageOrderValue.toFixed(2)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.customerInsights.newCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.customerInsights.returningCustomers} returning
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Inventory Alerts */}
      {(stats.inventoryAlerts.lowStock.length > 0 || stats.inventoryAlerts.outOfStock.length > 0) && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertCircle className="h-5 w-5" />
              Inventory Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.inventoryAlerts.outOfStock.length > 0 && (
                <div>
                  <Badge variant="destructive" className="mb-2">Out of Stock ({stats.inventoryAlerts.outOfStock.length})</Badge>
                  <div className="text-sm text-muted-foreground">
                    {stats.inventoryAlerts.outOfStock.slice(0, 3).map(product => product.name).join(', ')}
                    {stats.inventoryAlerts.outOfStock.length > 3 && ` and ${stats.inventoryAlerts.outOfStock.length - 3} more`}
                  </div>
                </div>
              )}
              {stats.inventoryAlerts.lowStock.length > 0 && (
                <div>
                  <Badge variant="secondary" className="mb-2">Low Stock ({stats.inventoryAlerts.lowStock.length})</Badge>
                  <div className="text-sm text-muted-foreground">
                    {stats.inventoryAlerts.lowStock.slice(0, 3).map(product => `${product.name} (${product.stock_quantity})`).join(', ')}
                    {stats.inventoryAlerts.lowStock.length > 3 && ` and ${stats.inventoryAlerts.lowStock.length - 3} more`}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className={`rounded-full p-2 ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue Trend Chart */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Revenue Trend (Last 6 Months)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={stats.monthlyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `$${value.toFixed(0)}`} />
                <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Revenue']} />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Daily Orders Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Daily Orders (Last 30 Days)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={stats.dailyOrders}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="orders" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Order Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Order Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={stats.orderStatusBreakdown}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ status, count }) => `${status}: ${count}`}
                >
                  {stats.orderStatusBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Categories */}
      {stats.topCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Top Categories by Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.topCategories} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={(value) => `$${value.toFixed(0)}`} />
                <YAxis dataKey="category" type="category" width={100} />
                <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Revenue']} />
                <Bar dataKey="revenue" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Recent Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recentOrders.map((order, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Order #{order.order_number}</p>
                    <p className="text-sm text-gray-600">
                      {order.user_profiles?.first_name} {order.user_profiles?.last_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">${Number(order.total_amount).toFixed(2)}</p>
                    <Badge variant={
                      order.status === 'delivered' ? 'default' : 
                      order.status === 'shipped' ? 'secondary' : 
                      'outline'
                    }>
                      {order.status}
                    </Badge>
                  </div>
                </div>
              ))}
              {stats.recentOrders.length === 0 && (
                <p className="text-center text-gray-500 py-4">No orders yet</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Featured Products */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Featured Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.popularProducts.map((product, index) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="h-12 w-12 rounded-lg bg-gray-200 overflow-hidden">
                    <img 
                      src={product.image_url} 
                      alt={product.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-600">${Number(product.price).toFixed(2)}</p>
                  </div>
                  <Badge variant={product.in_stock ? 'default' : 'destructive'}>
                    {product.in_stock ? 'In Stock' : 'Out of Stock'}
                  </Badge>
                </div>
              ))}
              {stats.popularProducts.length === 0 && (
                <p className="text-center text-gray-500 py-4">No featured products</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};