import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { supabase } from '@/integrations/supabase/client';
import { Save, Home } from 'lucide-react';
import { toast } from 'sonner';

interface HomepageSettings {
  hero_title: string;
  hero_subtitle: string;
  hero_image_url: string;
  about_title: string;
  about_description: string;
  about_image_url: string;
  featured_portfolio_title: string;
  featured_products_title: string;
  contact_cta_title: string;
  contact_cta_description: string;
}

export const AdminHomepage = () => {
  const [settings, setSettings] = useState<HomepageSettings>({
    hero_title: '',
    hero_subtitle: '',
    hero_image_url: '',
    about_title: '',
    about_description: '',
    about_image_url: '',
    featured_portfolio_title: '',
    featured_products_title: '',
    contact_cta_title: '',
    contact_cta_description: '',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchHomepageSettings();
  }, []);

  const fetchHomepageSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('site_settings')
        .select('key, value')
        .in('key', [
          'hero_title',
          'hero_subtitle', 
          'hero_image_url',
          'about_title',
          'about_description',
          'about_image_url',
          'featured_portfolio_title',
          'featured_products_title',
          'contact_cta_title',
          'contact_cta_description'
        ]);

      if (error) throw error;

      const settingsObj: any = {};
      data?.forEach((setting) => {
        // Handle both JSONB and text values
        let value = setting.value;
        if (typeof value === 'string') {
          // If it's a string, try to parse as JSON, otherwise use as-is
          try {
            value = JSON.parse(value);
          } catch {
            // If parsing fails, remove quotes if present
            value = value.replace(/^"|"$/g, '');
          }
        }
        settingsObj[setting.key] = value;
      });

      setSettings({
        hero_title: settingsObj.hero_title || 'Depths of Perception',
        hero_subtitle: settingsObj.hero_subtitle || 'Custom aquarium decorations by a renowned sculptor',
        hero_image_url: settingsObj.hero_image_url || '',
        about_title: settingsObj.about_title || 'About the Artist',
        about_description: settingsObj.about_description || '',
        about_image_url: settingsObj.about_image_url || '',
        featured_portfolio_title: settingsObj.featured_portfolio_title || 'Featured Artwork',
        featured_products_title: settingsObj.featured_products_title || 'Featured Products',
        contact_cta_title: settingsObj.contact_cta_title || 'Ready to Transform Your Aquarium?',
        contact_cta_description: settingsObj.contact_cta_description || 'Contact us for custom pieces',
      });
    } catch (error) {
      console.error('Error fetching homepage settings:', error);
      toast.error('Failed to fetch homepage settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      // Validate required fields
      if (!settings.hero_title?.trim()) {
        toast.error('Hero title is required');
        setSaving(false);
        return;
      }

      const updates = Object.entries(settings).map(([key, value]) => ({
        key,
        value: JSON.stringify(value), // Always store as JSON string for JSONB compatibility
        description: `Homepage ${key.replace('_', ' ')}`
      }));

      for (const update of updates) {
        const { error } = await supabase
          .from('site_settings')
          .upsert(update, { onConflict: 'key' });

        if (error) throw error;
      }

      toast.success('Homepage settings saved successfully!');
    } catch (error: any) {
      console.error('Error saving homepage settings:', error);
      toast.error(`Failed to save homepage settings: ${error.message || 'Unknown error'}`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading homepage settings...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Homepage Management</h2>
        <p className="text-muted-foreground">
          Customize your homepage content and appearance
        </p>
      </div>

      <div className="grid gap-6">
        {/* Hero Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Home className="h-5 w-5" />
              Hero Section
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="hero_title">Hero Title</Label>
              <Input
                id="hero_title"
                value={settings.hero_title}
                onChange={(e) => setSettings({ ...settings, hero_title: e.target.value })}
                placeholder="Main headline for your homepage"
              />
            </div>
            
            <div>
              <Label htmlFor="hero_subtitle">Hero Subtitle</Label>
              <Input
                id="hero_subtitle"
                value={settings.hero_subtitle}
                onChange={(e) => setSettings({ ...settings, hero_subtitle: e.target.value })}
                placeholder="Supporting text for your headline"
              />
            </div>
            
            <div>
              <ImageUpload
                bucket="homepage-images"
                currentImageUrl={settings.hero_image_url}
                onImageUploaded={(url) => setSettings({ ...settings, hero_image_url: url })}
                onImageRemoved={() => setSettings({ ...settings, hero_image_url: '' })}
                label="Hero Background Image"
                description="Upload a high-quality hero background image (JPEG, PNG, WebP, GIF)"
                aspectRatio="landscape"
              />
            </div>
          </CardContent>
        </Card>

        {/* About Section */}
        <Card>
          <CardHeader>
            <CardTitle>About Section</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="about_title">About Section Title</Label>
              <Input
                id="about_title"
                value={settings.about_title}
                onChange={(e) => setSettings({ ...settings, about_title: e.target.value })}
                placeholder="About section heading"
              />
            </div>
            
            <div>
              <Label htmlFor="about_description">About Description</Label>
              <Textarea
                id="about_description"
                value={settings.about_description}
                onChange={(e) => setSettings({ ...settings, about_description: e.target.value })}
                placeholder="Tell your story and artistic background"
                rows={4}
              />
            </div>
            
            <div>
              <ImageUpload
                bucket="homepage-images"
                currentImageUrl={settings.about_image_url}
                onImageUploaded={(url) => setSettings({ ...settings, about_image_url: url })}
                onImageRemoved={() => setSettings({ ...settings, about_image_url: '' })}
                label="About Section Image"
                description="Upload an image for the about section (JPEG, PNG, WebP, GIF)"
                aspectRatio="portrait"
              />
            </div>
          </CardContent>
        </Card>

        {/* Featured Sections */}
        <Card>
          <CardHeader>
            <CardTitle>Featured Sections</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="featured_portfolio_title">Featured Portfolio Title</Label>
              <Input
                id="featured_portfolio_title"
                value={settings.featured_portfolio_title}
                onChange={(e) => setSettings({ ...settings, featured_portfolio_title: e.target.value })}
                placeholder="Title for featured portfolio section"
              />
            </div>
            
            <div>
              <Label htmlFor="featured_products_title">Featured Products Title</Label>
              <Input
                id="featured_products_title"
                value={settings.featured_products_title}
                onChange={(e) => setSettings({ ...settings, featured_products_title: e.target.value })}
                placeholder="Title for featured products section"
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact CTA Section */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Call-to-Action</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="contact_cta_title">CTA Title</Label>
              <Input
                id="contact_cta_title"
                value={settings.contact_cta_title}
                onChange={(e) => setSettings({ ...settings, contact_cta_title: e.target.value })}
                placeholder="Call-to-action heading"
              />
            </div>
            
            <div>
              <Label htmlFor="contact_cta_description">CTA Description</Label>
              <Textarea
                id="contact_cta_description"
                value={settings.contact_cta_description}
                onChange={(e) => setSettings({ ...settings, contact_cta_description: e.target.value })}
                placeholder="Encourage visitors to get in touch"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSaveSettings} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
};