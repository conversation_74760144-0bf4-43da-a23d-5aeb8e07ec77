import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ShoppingCart, Heart, Share2, Minus, Plus, ChevronLeft, ChevronRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/contexts/CartContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useAuth } from "@/hooks/useAuth";
import AuthModal from "./AuthModal";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  material: string;
  size: string;
  in_stock: boolean;
}

interface ProductVariation {
  id: string;
  type: 'size' | 'color' | 'material';
  value: string;
  price_modifier: number;
  image_url?: string;
  in_stock: boolean;
}

interface ProductModalProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
}

const ProductModal = ({ product, isOpen, onClose }: ProductModalProps) => {
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [selectedMaterial, setSelectedMaterial] = useState<string>('');
  const [quantity, setQuantity] = useState(1);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const { toast } = useToast();
  const { addItem, openCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { user } = useAuth();

  if (!product) return null;

  // Mock data for variations and multiple images
  const variations: ProductVariation[] = [
    { id: '1', type: 'size', value: 'Small', price_modifier: 0, image_url: product?.image_url, in_stock: true },
    { id: '2', type: 'size', value: 'Medium', price_modifier: 25, image_url: product?.image_url, in_stock: true },
    { id: '3', type: 'size', value: 'Large', price_modifier: 50, image_url: product?.image_url, in_stock: false },
    { id: '4', type: 'color', value: 'Ocean Blue', price_modifier: 0, image_url: product?.image_url, in_stock: true },
    { id: '5', type: 'color', value: 'Coral Pink', price_modifier: 10, image_url: product?.image_url, in_stock: true },
    { id: '6', type: 'color', value: 'Sea Green', price_modifier: 15, image_url: product?.image_url, in_stock: true },
  ];

  // Mock multiple images for slideshow
  const productImages = [
    product?.image_url || '',
    product?.image_url || '',
    product?.image_url || '',
  ].filter(Boolean);

  const sizeOptions = variations.filter(v => v.type === 'size');
  const colorOptions = variations.filter(v => v.type === 'color');
  const materialOptions = variations.filter(v => v.type === 'material');

  const getCurrentVariationImage = () => {
    const selectedVariation = variations.find(v => 
      (v.type === 'size' && v.value === selectedSize) ||
      (v.type === 'color' && v.value === selectedColor) ||
      (v.type === 'material' && v.value === selectedMaterial)
    );
    return selectedVariation?.image_url || productImages[currentImageIndex];
  };

  const calculatePrice = () => {
    let basePrice = product.price;
    const selectedVariations = [
      variations.find(v => v.type === 'size' && v.value === selectedSize),
      variations.find(v => v.type === 'color' && v.value === selectedColor),
      variations.find(v => v.type === 'material' && v.value === selectedMaterial),
    ].filter(Boolean);

    selectedVariations.forEach(variation => {
      if (variation) basePrice += variation.price_modifier;
    });

    return basePrice;
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % productImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
  };

  const handleAddToCart = async () => {
    setIsAddingToCart(true);

    try {
      // Calculate final price with variations
      const finalPrice = calculatePrice();

      // Create cart item with selected options
      const cartItem = {
        id: `${product.id}-${selectedSize}-${selectedColor}-${selectedMaterial}`,
        name: product.name,
        price: finalPrice,
        image_url: product.image_url,
        category: product.category,
        size: selectedSize || product.size,
        material: selectedMaterial || product.material,
      };

      // Add multiple quantities if needed
      for (let i = 0; i < quantity; i++) {
        addItem(cartItem);
      }

      toast({
        title: "Added to Cart",
        description: `${quantity}x ${product.name} added to your cart.`,
      });

      // Open cart sidebar to show the added item
      openCart();
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleWishlist = async () => {
    if (!user) {
      setShowAuthModal(true);
      return;
    }

    try {
      const productInWishlist = isInWishlist(product.id);

      if (productInWishlist) {
        await removeFromWishlist(product.id);
      } else {
        await addToWishlist({
          id: product.id,
          name: product.name,
          price: product.price,
          image_url: product.image_url,
          category: product.category,
          size: product.size,
          material: product.material,
        });
      }
    } catch (error) {
      console.error('Error handling wishlist:', error);
    }
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast({
      title: "Link Copied",
      description: "Product link copied to clipboard.",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="sr-only">Product Details</DialogTitle>
          <DialogDescription className="sr-only">
            View detailed information about {product.name} including images, specifications, and purchase options.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid md:grid-cols-2 gap-8">
          {/* Product Images Slideshow */}
          <div className="space-y-4">
            <div className="relative aspect-square rounded-lg overflow-hidden bg-muted">
              <img 
                src={getCurrentVariationImage()} 
                alt={product.name}
                className="w-full h-full object-cover"
              />
              {productImages.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                    {productImages.map((_, index) => (
                      <button
                        key={index}
                        className={`w-2 h-2 rounded-full ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                        }`}
                        onClick={() => setCurrentImageIndex(index)}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge variant="secondary">{product.category}</Badge>
                <Badge variant="outline">{product.size}</Badge>
                <Badge variant="outline">{product.material}</Badge>
              </div>
            </div>
            
            <div className="text-4xl font-bold text-primary">
              R{calculatePrice().toFixed(2)}
              {calculatePrice() !== product.price && (
                <span className="text-sm text-muted-foreground ml-2">
                  (Base: R{product.price.toFixed(2)})
                </span>
              )}
            </div>
            
            <p className="text-muted-foreground leading-relaxed">
              {product.description}
            </p>
            
            {product.in_stock ? (
              <div className="space-y-4">
                {/* Product Variations */}
                {sizeOptions.length > 0 && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">Size</label>
                    <Select value={selectedSize} onValueChange={setSelectedSize}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select size" />
                      </SelectTrigger>
                      <SelectContent>
                        {sizeOptions.map((option) => (
                          <SelectItem 
                            key={option.id} 
                            value={option.value}
                            disabled={!option.in_stock}
                          >
                            {option.value} 
                            {option.price_modifier > 0 && ` (+R${option.price_modifier})`}
                            {!option.in_stock && ' (Out of Stock)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {colorOptions.length > 0 && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">Color</label>
                    <Select value={selectedColor} onValueChange={setSelectedColor}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select color" />
                      </SelectTrigger>
                      <SelectContent>
                        {colorOptions.map((option) => (
                          <SelectItem 
                            key={option.id} 
                            value={option.value}
                            disabled={!option.in_stock}
                          >
                            {option.value}
                            {option.price_modifier > 0 && ` (+R${option.price_modifier})`}
                            {!option.in_stock && ' (Out of Stock)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">Quantity:</span>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="w-8 text-center">{quantity}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    size="lg"
                    onClick={handleAddToCart}
                    disabled={isAddingToCart}
                  >
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    {isAddingToCart ? "Adding..." : `Add ${quantity} to Cart - R${(calculatePrice() * quantity).toFixed(2)}`}
                  </Button>
                  
                  <div className="flex gap-2">
                    <Button
                      variant={isInWishlist(product.id) ? "default" : "outline"}
                      className="flex-1"
                      onClick={handleWishlist}
                    >
                      <Heart className={`mr-2 h-4 w-4 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                      {isInWishlist(product.id) ? 'In Wishlist' : 'Wishlist'}
                    </Button>
                    <Button 
                      variant="outline" 
                      className="flex-1"
                      onClick={handleShare}
                    >
                      <Share2 className="mr-2 h-4 w-4" />
                      Share
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <Badge variant="destructive" className="text-base px-4 py-2">
                  Out of Stock
                </Badge>
                <p className="text-muted-foreground">
                  This item is currently out of stock. Contact us for custom orders or to be notified when it's available again.
                </p>
                <Button variant="outline" className="w-full">
                  Contact for Custom Order
                </Button>
              </div>
            )}
            
            {/* Product Features */}
            <div className="pt-6 border-t">
              <h3 className="font-semibold mb-3">Product Features</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Handcrafted with premium materials</li>
                <li>• Safe for all aquarium environments</li>
                <li>• Easy to clean and maintain</li>
                <li>• Custom sizing available on request</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultTab="login"
        onSuccess={() => {
          toast({
            title: "Welcome!",
            description: "You can now add items to your wishlist.",
          });
        }}
      />
    </Dialog>
  );
};

export default ProductModal;