import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ImageUpload } from '@/components/ui/ImageUpload';
import { supabase } from '@/integrations/supabase/client';
import { Save, User, Palette } from 'lucide-react';
import { toast } from 'sonner';

interface ArtistProfile {
  id: string;
  name: string;
  bio: string;
  full_biography: string;
  portrait_url: string;
  hero_image_url: string;
  website_url: string;
  social_instagram: string;
  social_facebook: string;
  years_experience: number;
  total_pieces_created: number;
}

export const AdminArtist = () => {
  const [artist, setArtist] = useState<ArtistProfile>({
    id: '',
    name: '',
    bio: '',
    full_biography: '',
    portrait_url: '',
    hero_image_url: '',
    website_url: '',
    social_instagram: '',
    social_facebook: '',
    years_experience: 0,
    total_pieces_created: 0,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchArtistProfile();
  }, []);

  const fetchArtistProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('artist_profile')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setArtist(data);
      }
    } catch (error) {
      console.error('Error fetching artist profile:', error);
      toast.error('Failed to fetch artist profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      // Validate required fields
      if (!artist.name?.trim()) {
        toast.error('Artist name is required');
        setSaving(false);
        return;
      }

      const profileData = {
        name: artist.name.trim(),
        bio: artist.bio || '',
        full_biography: artist.full_biography || '',
        portrait_url: artist.portrait_url || '',
        hero_image_url: artist.hero_image_url || '',
        website_url: artist.website_url || '',
        social_instagram: artist.social_instagram || '',
        social_facebook: artist.social_facebook || '',
        years_experience: artist.years_experience || 0,
        total_pieces_created: artist.total_pieces_created || 0,
        updated_at: new Date().toISOString(),
      };

      if (artist.id) {
        // Update existing profile
        const { error } = await supabase
          .from('artist_profile')
          .update(profileData)
          .eq('id', artist.id);

        if (error) throw error;
      } else {
        // Create new profile
        const { data, error } = await supabase
          .from('artist_profile')
          .insert(profileData)
          .select()
          .single();

        if (error) throw error;
        if (data) {
          setArtist(prev => ({ ...prev, id: data.id }));
        }
      }

      toast.success('Artist profile saved successfully!');
    } catch (error: any) {
      console.error('Error saving artist profile:', error);
      toast.error(`Failed to save artist profile: ${error.message || 'Unknown error'}`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading artist profile...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Artist Profile</h2>
          <p className="text-muted-foreground">
            Manage the artist information displayed on your website
          </p>
        </div>
        <Button onClick={handleSaveProfile} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>

      <div className="grid gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Artist Name *</Label>
              <Input
                id="name"
                value={artist.name}
                onChange={(e) => setArtist({ ...artist, name: e.target.value })}
                placeholder="Enter artist name"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="bio">Short Bio</Label>
              <Textarea
                id="bio"
                value={artist.bio}
                onChange={(e) => setArtist({ ...artist, bio: e.target.value })}
                placeholder="Brief description for cards and previews"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="full_biography">Full Biography</Label>
              <Textarea
                id="full_biography"
                value={artist.full_biography}
                onChange={(e) => setArtist({ ...artist, full_biography: e.target.value })}
                placeholder="Detailed biography for the about section"
                rows={6}
              />
            </div>
          </CardContent>
        </Card>

        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Images
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <ImageUpload
                  bucket="artist-images"
                  currentImageUrl={artist.portrait_url}
                  onImageUploaded={(url) => setArtist({ ...artist, portrait_url: url })}
                  onImageRemoved={() => setArtist({ ...artist, portrait_url: '' })}
                  label="Portrait Image"
                  description="Professional headshot or portrait (square format recommended)"
                  aspectRatio="square"
                />
              </div>
              
              <div>
                <ImageUpload
                  bucket="artist-images"
                  currentImageUrl={artist.hero_image_url}
                  onImageUploaded={(url) => setArtist({ ...artist, hero_image_url: url })}
                  onImageRemoved={() => setArtist({ ...artist, hero_image_url: '' })}
                  label="Hero Background Image"
                  description="Large background image for hero sections (landscape format)"
                  aspectRatio="landscape"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact & Social */}
        <Card>
          <CardHeader>
            <CardTitle>Contact & Social Media</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="website_url">Website URL</Label>
              <Input
                id="website_url"
                value={artist.website_url}
                onChange={(e) => setArtist({ ...artist, website_url: e.target.value })}
                placeholder="https://your-website.com"
                type="url"
              />
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="social_instagram">Instagram Handle</Label>
                <Input
                  id="social_instagram"
                  value={artist.social_instagram}
                  onChange={(e) => setArtist({ ...artist, social_instagram: e.target.value })}
                  placeholder="@username"
                />
              </div>
              
              <div>
                <Label htmlFor="social_facebook">Facebook Page</Label>
                <Input
                  id="social_facebook"
                  value={artist.social_facebook}
                  onChange={(e) => setArtist({ ...artist, social_facebook: e.target.value })}
                  placeholder="Facebook page URL or handle"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Experience & Statistics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="years_experience">Years of Experience</Label>
                <Input
                  id="years_experience"
                  type="number"
                  min="0"
                  value={artist.years_experience}
                  onChange={(e) => setArtist({ ...artist, years_experience: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>
              
              <div>
                <Label htmlFor="total_pieces_created">Total Pieces Created</Label>
                <Input
                  id="total_pieces_created"
                  type="number"
                  min="0"
                  value={artist.total_pieces_created}
                  onChange={(e) => setArtist({ ...artist, total_pieces_created: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
