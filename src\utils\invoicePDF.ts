import jsPDF from 'jspdf';

interface InvoiceData {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  status: string;
  payment_method?: string;
  payment_reference?: string;
  paid_at?: string;
  notes?: string;
  terms?: string;
  invoice_line_items?: InvoiceLineItem[];
  customer?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    address?: {
      address_line_1?: string;
      address_line_2?: string;
      city?: string;
      state?: string;
      postal_code?: string;
      country?: string;
    };
  };
}

interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export const generateInvoicePDF = async (invoice: InvoiceData): Promise<void> => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.width;
  const pageHeight = pdf.internal.pageSize.height;
  const margin = 20;
  let yPosition = margin;

  // Helper function to add text with automatic line wrapping
  const addText = (text: string, x: number, y: number, options?: any) => {
    const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);
    pdf.text(lines, x, y, options);
    return y + (lines.length * 6);
  };

  // Helper function to format currency
  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Company Header
  pdf.setFontSize(24);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Depths of Perception Aquascapes', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Custom Aquarium Art & Design', margin, yPosition);
  yPosition += 8;
  pdf.text('Email: <EMAIL>', margin, yPosition);
  yPosition += 8;
  pdf.text('Phone: (*************', margin, yPosition);
  yPosition += 20;

  // Invoice Title and Number
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('INVOICE', pageWidth - margin - 40, 30, { align: 'right' });
  
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Invoice #: ${invoice.invoice_number}`, pageWidth - margin - 60, 45, { align: 'right' });
  pdf.text(`Date: ${formatDate(invoice.invoice_date)}`, pageWidth - margin - 60, 55, { align: 'right' });
  
  if (invoice.due_date) {
    pdf.text(`Due Date: ${formatDate(invoice.due_date)}`, pageWidth - margin - 60, 65, { align: 'right' });
  }

  // Customer Information
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Bill To:', margin, yPosition);
  yPosition += 10;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  if (invoice.customer) {
    const customer = invoice.customer;
    if (customer.first_name || customer.last_name) {
      pdf.text(`${customer.first_name || ''} ${customer.last_name || ''}`.trim(), margin, yPosition);
      yPosition += 6;
    }
    if (customer.email) {
      pdf.text(customer.email, margin, yPosition);
      yPosition += 6;
    }
    if (customer.phone) {
      pdf.text(customer.phone, margin, yPosition);
      yPosition += 6;
    }
    if (customer.address) {
      const addr = customer.address;
      if (addr.address_line_1) {
        pdf.text(addr.address_line_1, margin, yPosition);
        yPosition += 6;
      }
      if (addr.address_line_2) {
        pdf.text(addr.address_line_2, margin, yPosition);
        yPosition += 6;
      }
      const cityStateZip = [addr.city, addr.state, addr.postal_code].filter(Boolean).join(', ');
      if (cityStateZip) {
        pdf.text(cityStateZip, margin, yPosition);
        yPosition += 6;
      }
      if (addr.country && addr.country !== 'US') {
        pdf.text(addr.country, margin, yPosition);
        yPosition += 6;
      }
    }
  }

  yPosition += 15;

  // Invoice Items Table
  const tableStartY = yPosition;
  const colWidths = [80, 20, 30, 30, 30]; // Description, Qty, Unit Price, Total
  const colPositions = [margin];
  for (let i = 1; i < colWidths.length; i++) {
    colPositions.push(colPositions[i - 1] + colWidths[i - 1]);
  }

  // Table Header
  pdf.setFillColor(240, 240, 240);
  pdf.rect(margin, yPosition, pageWidth - 2 * margin, 10, 'F');
  
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Description', colPositions[0] + 2, yPosition + 7);
  pdf.text('Qty', colPositions[1] + 2, yPosition + 7);
  pdf.text('Unit Price', colPositions[2] + 2, yPosition + 7);
  pdf.text('Total', colPositions[3] + 2, yPosition + 7);
  
  yPosition += 15;

  // Table Rows
  pdf.setFont('helvetica', 'normal');
  if (invoice.invoice_line_items) {
    invoice.invoice_line_items.forEach((item, index) => {
      // Add new page if needed
      if (yPosition > pageHeight - 50) {
        pdf.addPage();
        yPosition = margin;
      }

      const rowHeight = 8;
      
      // Alternate row background
      if (index % 2 === 0) {
        pdf.setFillColor(250, 250, 250);
        pdf.rect(margin, yPosition - 2, pageWidth - 2 * margin, rowHeight, 'F');
      }

      // Description (with text wrapping)
      const descLines = pdf.splitTextToSize(item.description, colWidths[0] - 4);
      pdf.text(descLines, colPositions[0] + 2, yPosition + 5);
      
      // Quantity
      pdf.text(item.quantity.toString(), colPositions[1] + 2, yPosition + 5);
      
      // Unit Price
      pdf.text(formatCurrency(item.unit_price), colPositions[2] + 2, yPosition + 5);
      
      // Total
      pdf.text(formatCurrency(item.total_price), colPositions[3] + 2, yPosition + 5);

      yPosition += Math.max(rowHeight, descLines.length * 4 + 4);
    });
  }

  yPosition += 10;

  // Totals Section
  const totalsX = pageWidth - margin - 80;
  
  pdf.setFont('helvetica', 'normal');
  pdf.text('Subtotal:', totalsX, yPosition);
  pdf.text(formatCurrency(invoice.subtotal), totalsX + 40, yPosition, { align: 'right' });
  yPosition += 8;

  if (invoice.discount_amount > 0) {
    pdf.text('Discount:', totalsX, yPosition);
    pdf.text(`-${formatCurrency(invoice.discount_amount)}`, totalsX + 40, yPosition, { align: 'right' });
    yPosition += 8;
  }

  if (invoice.shipping_amount > 0) {
    pdf.text('Shipping:', totalsX, yPosition);
    pdf.text(formatCurrency(invoice.shipping_amount), totalsX + 40, yPosition, { align: 'right' });
    yPosition += 8;
  }

  pdf.text('Tax:', totalsX, yPosition);
  pdf.text(formatCurrency(invoice.tax_amount), totalsX + 40, yPosition, { align: 'right' });
  yPosition += 8;

  // Total line
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.line(totalsX, yPosition, totalsX + 40, yPosition);
  yPosition += 8;
  pdf.text('Total:', totalsX, yPosition);
  pdf.text(formatCurrency(invoice.total_amount), totalsX + 40, yPosition, { align: 'right' });
  yPosition += 15;

  // Payment Information
  if (invoice.payment_method || invoice.paid_at) {
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Payment Information:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    if (invoice.payment_method) {
      pdf.text(`Payment Method: ${invoice.payment_method}`, margin, yPosition);
      yPosition += 6;
    }
    if (invoice.payment_reference) {
      pdf.text(`Reference: ${invoice.payment_reference}`, margin, yPosition);
      yPosition += 6;
    }
    if (invoice.paid_at) {
      pdf.text(`Paid On: ${formatDate(invoice.paid_at)}`, margin, yPosition);
      yPosition += 6;
    }
    yPosition += 10;
  }

  // Notes
  if (invoice.notes) {
    pdf.setFont('helvetica', 'bold');
    pdf.text('Notes:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    yPosition = addText(invoice.notes, margin, yPosition);
    yPosition += 10;
  }

  // Terms
  if (invoice.terms) {
    pdf.setFont('helvetica', 'bold');
    pdf.text('Terms & Conditions:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    yPosition = addText(invoice.terms, margin, yPosition);
  }

  // Footer
  const footerY = pageHeight - 20;
  pdf.setFontSize(8);
  pdf.setFont('helvetica', 'italic');
  pdf.text('Thank you for your business!', pageWidth / 2, footerY, { align: 'center' });

  // Save the PDF
  pdf.save(`invoice-${invoice.invoice_number}.pdf`);
};
