import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  MessageSquare, 
  Send, 
  Plus, 
  Eye, 
  Calendar, 
  User,
  AlertCircle,
  CheckCircle,
  Paperclip,
  Search
} from 'lucide-react';
import { toast } from 'sonner';

interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  recipient_id: string;
  sender_type: 'customer' | 'admin';
  subject?: string;
  message: string;
  attachments?: string[];
  is_read: boolean;
  is_important: boolean;
  related_order_id?: string;
  related_custom_request_id?: string;
  created_at: string;
  updated_at: string;
}

interface Conversation {
  conversation_id: string;
  messages: Message[];
  lastMessage: Message;
  unreadCount: number;
}

export const CustomerMessages = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [showNewMessageDialog, setShowNewMessageDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // New message form state
  const [newMessage, setNewMessage] = useState({
    subject: '',
    message: '',
    is_important: false
  });

  // Reply form state
  const [replyMessage, setReplyMessage] = useState('');

  useEffect(() => {
    if (user) {
      fetchMessages();
    }
  }, [user]);

  const fetchMessages = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('customer_messages')
        .select('*')
        .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group messages by conversation
      const conversationMap = new Map<string, Message[]>();
      
      data?.forEach(message => {
        const convId = message.conversation_id;
        if (!conversationMap.has(convId)) {
          conversationMap.set(convId, []);
        }
        conversationMap.get(convId)!.push(message);
      });

      // Convert to conversation objects
      const convs: Conversation[] = Array.from(conversationMap.entries()).map(([convId, messages]) => {
        const sortedMessages = messages.sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
        const unreadCount = messages.filter(m => 
          m.recipient_id === user.id && !m.is_read
        ).length;

        return {
          conversation_id: convId,
          messages: sortedMessages,
          lastMessage: sortedMessages[sortedMessages.length - 1],
          unreadCount
        };
      });

      // Sort conversations by last message date
      convs.sort((a, b) => 
        new Date(b.lastMessage.created_at).getTime() - new Date(a.lastMessage.created_at).getTime()
      );

      setConversations(convs);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('Failed to fetch messages');
    } finally {
      setLoading(false);
    }
  };

  const startNewConversation = async () => {
    if (!user) return;

    try {
      if (!newMessage.subject.trim() || !newMessage.message.trim()) {
        toast.error('Please fill in the subject and message');
        return;
      }

      // Get admin user (in a real app, you'd have a way to select the recipient)
      const { data: adminUsers } = await supabase
        .from('user_profiles')
        .select('id')
        .in('role', ['admin', 'super_admin'])
        .limit(1);

      if (!adminUsers || adminUsers.length === 0) {
        toast.error('No admin users found');
        return;
      }

      const adminId = adminUsers[0].id;
      
      // Generate conversation ID
      const conversationId = crypto.randomUUID();

      const { error } = await supabase
        .from('customer_messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          recipient_id: adminId,
          sender_type: 'customer',
          subject: newMessage.subject.trim(),
          message: newMessage.message.trim(),
          is_important: newMessage.is_important
        });

      if (error) throw error;

      toast.success('Message sent successfully!');
      setShowNewMessageDialog(false);
      setNewMessage({
        subject: '',
        message: '',
        is_important: false
      });
      fetchMessages();
    } catch (error: any) {
      console.error('Error sending message:', error);
      toast.error(`Failed to send message: ${error.message}`);
    }
  };

  const sendReply = async (conversationId: string) => {
    if (!user || !replyMessage.trim()) return;

    try {
      // Get the conversation to find the recipient
      const conversation = conversations.find(c => c.conversation_id === conversationId);
      if (!conversation) return;

      // Find the other participant in the conversation
      const lastMessage = conversation.lastMessage;
      const recipientId = lastMessage.sender_id === user.id ? lastMessage.recipient_id : lastMessage.sender_id;

      const { error } = await supabase
        .from('customer_messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          recipient_id: recipientId,
          sender_type: 'customer',
          message: replyMessage.trim()
        });

      if (error) throw error;

      toast.success('Reply sent successfully!');
      setReplyMessage('');
      fetchMessages();
    } catch (error: any) {
      console.error('Error sending reply:', error);
      toast.error(`Failed to send reply: ${error.message}`);
    }
  };

  const markAsRead = async (messageId: string) => {
    try {
      const { error } = await supabase
        .from('customer_messages')
        .update({ is_read: true })
        .eq('id', messageId)
        .eq('recipient_id', user?.id);

      if (error) throw error;
      fetchMessages();
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const filteredConversations = conversations.filter(conv =>
    !searchTerm || 
    conv.lastMessage.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.lastMessage.message.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedConv = conversations.find(c => c.conversation_id === selectedConversation);

  if (loading) {
    return <div className="text-center py-8">Loading your messages...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Messages</h2>
          <p className="text-muted-foreground">
            Communicate directly with the artist and admin team
          </p>
        </div>
        <Dialog open={showNewMessageDialog} onOpenChange={setShowNewMessageDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Message
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Send New Message</DialogTitle>
            </DialogHeader>
            <NewMessageForm 
              message={newMessage}
              setMessage={setNewMessage}
              onSend={startNewConversation}
              onCancel={() => setShowNewMessageDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Conversations List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Conversations
              </CardTitle>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search messages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1">
                {filteredConversations.length > 0 ? (
                  filteredConversations.map((conv) => (
                    <div
                      key={conv.conversation_id}
                      className={`p-4 cursor-pointer hover:bg-muted/50 border-b ${
                        selectedConversation === conv.conversation_id ? 'bg-muted' : ''
                      }`}
                      onClick={() => {
                        setSelectedConversation(conv.conversation_id);
                        // Mark unread messages as read
                        conv.messages
                          .filter(m => m.recipient_id === user?.id && !m.is_read)
                          .forEach(m => markAsRead(m.id));
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="font-medium truncate">
                              {conv.lastMessage.subject || 'No Subject'}
                            </p>
                            {conv.lastMessage.is_important && (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            {conv.unreadCount > 0 && (
                              <Badge variant="destructive" className="text-xs">
                                {conv.unreadCount}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground truncate mt-1">
                            {conv.lastMessage.message}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span className="text-xs text-muted-foreground">
                                {conv.lastMessage.sender_type === 'customer' ? 'You' : 'Admin'}
                              </span>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatDate(conv.lastMessage.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-8 text-center text-muted-foreground">
                    {searchTerm ? 'No messages match your search.' : 'No conversations yet.'}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Message Thread */}
        <div className="lg:col-span-2">
          {selectedConv ? (
            <Card className="h-[600px] flex flex-col">
              <CardHeader className="border-b">
                <CardTitle className="flex items-center gap-2">
                  {selectedConv.lastMessage.subject || 'No Subject'}
                  {selectedConv.lastMessage.is_important && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </CardTitle>
              </CardHeader>
              
              {/* Messages */}
              <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                {selectedConv.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[70%] rounded-lg p-3 ${
                        message.sender_id === user?.id
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium">
                          {message.sender_type === 'customer' ? 'You' : 'Admin'}
                        </span>
                        <span className="text-xs opacity-70">
                          {formatDate(message.created_at)}
                        </span>
                        {!message.is_read && message.recipient_id === user?.id && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full" />
                        )}
                      </div>
                      <p className="text-sm whitespace-pre-wrap">{message.message}</p>
                      {message.attachments && message.attachments.length > 0 && (
                        <div className="mt-2 flex items-center gap-1 text-xs opacity-70">
                          <Paperclip className="h-3 w-3" />
                          {message.attachments.length} attachment(s)
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>

              {/* Reply Form */}
              <div className="border-t p-4">
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Type your reply..."
                    value={replyMessage}
                    onChange={(e) => setReplyMessage(e.target.value)}
                    className="flex-1 min-h-[60px]"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendReply(selectedConv.conversation_id);
                      }
                    }}
                  />
                  <Button 
                    onClick={() => sendReply(selectedConv.conversation_id)}
                    disabled={!replyMessage.trim()}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            <Card className="h-[600px] flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <MessageSquare className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
                <p>Choose a conversation from the list to view messages</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

// New Message Form Component
const NewMessageForm = ({ 
  message, 
  setMessage, 
  onSend, 
  onCancel 
}: {
  message: any;
  setMessage: (message: any) => void;
  onSend: () => void;
  onCancel: () => void;
}) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="subject">Subject *</Label>
        <Input
          id="subject"
          value={message.subject}
          onChange={(e) => setMessage({ ...message, subject: e.target.value })}
          placeholder="Enter message subject"
          required
        />
      </div>

      <div>
        <Label htmlFor="message">Message *</Label>
        <Textarea
          id="message"
          value={message.message}
          onChange={(e) => setMessage({ ...message, message: e.target.value })}
          placeholder="Type your message here..."
          rows={6}
          required
        />
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="important"
          checked={message.is_important}
          onChange={(e) => setMessage({ ...message, is_important: e.target.checked })}
          className="rounded"
        />
        <Label htmlFor="important" className="text-sm">
          Mark as important
        </Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onSend}>
          <Send className="h-4 w-4 mr-2" />
          Send Message
        </Button>
      </div>
    </div>
  );
};
