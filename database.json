[{"table_name": "artist_profile", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "artist_profile", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "artist_profile", "column_name": "bio", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "full_biography", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "portrait_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "hero_image_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "website_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "social_instagram", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "social_facebook", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "years_experience", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "artist_profile", "column_name": "total_pieces_created", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "artist_profile", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "artist_profile", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "contact_submissions", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "contact_submissions", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "contact_submissions", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "contact_submissions", "column_name": "subject", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "contact_submissions", "column_name": "message", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "contact_submissions", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "contact_submissions", "column_name": "project_type", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "contact_submissions", "column_name": "budget_range", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "contact_submissions", "column_name": "is_read", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "contact_submissions", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "order_items", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "order_items", "column_name": "order_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "order_items", "column_name": "product_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "order_items", "column_name": "product_name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "order_items", "column_name": "product_price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "order_items", "column_name": "quantity", "data_type": "integer", "is_nullable": "NO", "column_default": "1"}, {"table_name": "order_items", "column_name": "total_price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "order_items", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "order_tracking", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "order_tracking", "column_name": "order_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "order_tracking", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_name": "order_tracking", "column_name": "notes", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "order_tracking", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "orders", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "orders", "column_name": "user_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"table_name": "orders", "column_name": "order_number", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "orders", "column_name": "status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'pending'::order_status"}, {"table_name": "orders", "column_name": "payment_status", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'pending'::payment_status"}, {"table_name": "orders", "column_name": "subtotal", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "orders", "column_name": "tax_amount", "data_type": "numeric", "is_nullable": "YES", "column_default": "0"}, {"table_name": "orders", "column_name": "shipping_amount", "data_type": "numeric", "is_nullable": "YES", "column_default": "0"}, {"table_name": "orders", "column_name": "total_amount", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "orders", "column_name": "shipping_address", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "orders", "column_name": "billing_address", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "orders", "column_name": "notes", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "orders", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "orders", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "portfolio_items", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "portfolio_items", "column_name": "title", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "portfolio_items", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "portfolio_items", "column_name": "image_url", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "portfolio_items", "column_name": "category", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "portfolio_items", "column_name": "materials", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "portfolio_items", "column_name": "dimensions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "portfolio_items", "column_name": "year_created", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "portfolio_items", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "portfolio_items", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "portfolio_items", "column_name": "is_available", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "portfolio_items", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "portfolio_items", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "products", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "products", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "products", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "products", "column_name": "image_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "category", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "products", "column_name": "material", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "size", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "dimensions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "weight", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "care_instructions", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "products", "column_name": "in_stock", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"table_name": "products", "column_name": "stock_quantity", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "products", "column_name": "featured", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"table_name": "products", "column_name": "sort_order", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "products", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "products", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "site_settings", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "gen_random_uuid()"}, {"table_name": "site_settings", "column_name": "key", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "site_settings", "column_name": "value", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "site_settings", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "site_settings", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "site_settings", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "user_profiles", "column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"table_name": "user_profiles", "column_name": "first_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "last_name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "email", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "user_profiles", "column_name": "phone", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "role", "data_type": "USER-DEFINED", "is_nullable": "YES", "column_default": "'customer'::user_role"}, {"table_name": "user_profiles", "column_name": "avatar_url", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "user_profiles", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "user_profiles", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]