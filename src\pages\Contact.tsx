import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Mail, Phone, MapPin, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/Navbar";
import FishAnimation from "@/components/FishAnimation";
import { Link } from "react-router-dom";

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    phone: '',
    project_type: '',
    budget_range: ''
  });
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate all required fields
    if (!formData.name.trim() || !formData.email.trim() || !formData.subject.trim() ||
        !formData.message.trim() || !formData.phone.trim() || !formData.project_type.trim() ||
        !formData.budget_range.trim()) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields marked with *",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    try {
      const { error } = await supabase
        .from('contact_submissions')
        .insert([formData]);

      if (error) throw error;

      toast({
        title: "Message sent successfully!",
        description: "Thank you for reaching out. I'll get back to you soon.",
      });

      setFormData({ name: '', email: '', subject: '', message: '', phone: '', project_type: '', budget_range: '' });
    } catch (error) {
      toast({
        title: "Error sending message",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Header */}
      <section className="pt-24 pb-8 bg-card relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 relative z-10">
          <Link to="/" className="inline-flex items-center text-muted-foreground hover:text-primary mb-6 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
          
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Get in Touch</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Ready to discuss your custom aquarium decor project? I'd love to hear about your vision and help bring it to life.
            </p>
          </div>
        </div>
      </section>

      <section className="py-12 relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Send a Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Full Name *</Label>
                        <Input
                          id="name"
                          name="name"
                          type="text"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="Your full name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="subject">Subject *</Label>
                      <Input
                        id="subject"
                        name="subject"
                        type="text"
                        required
                        value={formData.subject}
                        onChange={handleInputChange}
                        placeholder="What's this about?"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="phone">Phone Number *</Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          required
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+27 12 345 6789"
                        />
                      </div>
                      <div>
                        <Label htmlFor="project_type">Project Type *</Label>
                        <Input
                          id="project_type"
                          name="project_type"
                          type="text"
                          required
                          value={formData.project_type}
                          onChange={handleInputChange}
                          placeholder="e.g., Custom Sculpture, Aquarium Decor"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="budget_range">Budget Range *</Label>
                      <Input
                        id="budget_range"
                        name="budget_range"
                        type="text"
                        required
                        value={formData.budget_range}
                        onChange={handleInputChange}
                        placeholder="e.g., R5,000-R10,000, R10,000-R25,000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="message">Message *</Label>
                      <Textarea
                        id="message"
                        name="message"
                        required
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Tell me about your project, tank size, theme preferences, and any specific requirements..."
                        rows={6}
                      />
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="w-full"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
            
            {/* Contact Info & FAQ */}
            <div className="space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-sm text-muted-foreground">+****************</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Location</p>
                      <p className="text-sm text-muted-foreground">Los Angeles, CA</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">Response Time</p>
                      <p className="text-sm text-muted-foreground">Usually within 24 hours</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* FAQ */}
              <Card>
                <CardHeader>
                  <CardTitle>Frequently Asked Questions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-1">How long does custom work take?</h4>
                    <p className="text-sm text-muted-foreground">
                      Custom pieces typically take 2-4 weeks, depending on complexity and current workload.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-1">Do you ship internationally?</h4>
                    <p className="text-sm text-muted-foreground">
                      Yes! We carefully package all items for safe international shipping.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-1">Are your decorations fish-safe?</h4>
                    <p className="text-sm text-muted-foreground">
                      Absolutely. All materials are non-toxic and specifically chosen for aquarium use.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-1">Can you work with specific themes?</h4>
                    <p className="text-sm text-muted-foreground">
                      Yes! From natural reefs to fantasy landscapes, I love bringing unique visions to life.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 ocean-gradient text-white relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl font-bold mb-4">Let's Create Something Amazing</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Every aquarium tells a story. What story will yours tell?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/portfolio">
              <Button size="lg" variant="secondary">
                View Past Work
              </Button>
            </Link>
            <Link to="/shop">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Browse Ready-Made Pieces
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;