import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  FileText, 
  Download, 
  Eye, 
  Calendar, 
  DollarSign, 
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Search
} from 'lucide-react';
import { toast } from 'sonner';
import { generateInvoicePDF } from '@/utils/invoicePDF';

interface Invoice {
  id: string;
  invoice_number: string;
  invoice_date: string;
  due_date?: string;
  subtotal: number;
  tax_amount: number;
  shipping_amount: number;
  discount_amount: number;
  total_amount: number;
  status: string;
  payment_method?: string;
  payment_reference?: string;
  paid_at?: string;
  notes?: string;
  terms?: string;
  created_at: string;
  order_id?: string;
  custom_request_id?: string;
  invoice_line_items?: InvoiceLineItem[];
}

interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product_id?: string;
}

export const CustomerInvoices = () => {
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    if (user) {
      fetchInvoices();
    }
  }, [user]);

  useEffect(() => {
    filterInvoices();
  }, [invoices, searchTerm, statusFilter]);

  const fetchInvoices = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('customer_invoices')
        .select(`
          *,
          invoice_line_items(*)
        `)
        .eq('user_id', user.id)
        .order('invoice_date', { ascending: false });

      if (error) throw error;
      setInvoices(data || []);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      toast.error('Failed to fetch invoices');
    } finally {
      setLoading(false);
    }
  };

  const filterInvoices = () => {
    let filtered = invoices;

    if (searchTerm) {
      filtered = filtered.filter(invoice =>
        invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.payment_reference?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === statusFilter);
    }

    setFilteredInvoices(filtered);
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      draft: 'bg-gray-100 text-gray-800',
      sent: 'bg-blue-100 text-blue-800',
      paid: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <FileText className="h-4 w-4" />;
      case 'sent':
        return <Clock className="h-4 w-4" />;
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const downloadInvoice = async (invoice: Invoice) => {
    try {
      toast.info('Generating PDF invoice...');

      // Fetch additional customer data for the invoice
      const { data: customerData, error: customerError } = await supabase
        .from('user_profiles')
        .select('first_name, last_name, email, phone')
        .eq('id', user?.id)
        .single();

      if (customerError) {
        console.error('Error fetching customer data:', customerError);
      }

      // Fetch customer address if available
      const { data: addressData, error: addressError } = await supabase
        .from('customer_addresses')
        .select('*')
        .eq('user_id', user?.id)
        .eq('is_default', true)
        .single();

      if (addressError && addressError.code !== 'PGRST116') {
        console.error('Error fetching address data:', addressError);
      }

      // Prepare invoice data for PDF generation
      const invoiceData = {
        ...invoice,
        customer: {
          ...customerData,
          address: addressData ? {
            address_line_1: addressData.address_line_1,
            address_line_2: addressData.address_line_2,
            city: addressData.city,
            state: addressData.state,
            postal_code: addressData.postal_code,
            country: addressData.country,
          } : undefined,
        },
      };

      await generateInvoicePDF(invoiceData);
      toast.success('Invoice PDF downloaded successfully!');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF invoice');
    }
  };

  const isOverdue = (invoice: Invoice) => {
    if (!invoice.due_date || invoice.status === 'paid') return false;
    return new Date(invoice.due_date) < new Date();
  };

  if (loading) {
    return <div className="text-center py-8">Loading your invoices...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Invoices & Billing</h2>
          <p className="text-muted-foreground">
            View and manage your invoices and payment history
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invoices.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {invoices.filter(i => i.status === 'paid').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {invoices.filter(i => i.status === 'sent').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {invoices.filter(i => i.status === 'overdue' || isOverdue(i)).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by invoice number or payment reference..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Invoices</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* Invoices List */}
      <div className="space-y-4">
        {filteredInvoices.length > 0 ? (
          filteredInvoices.map((invoice) => (
            <Card key={invoice.id} className={isOverdue(invoice) ? 'border-red-200' : ''}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-lg">Invoice #{invoice.invoice_number}</CardTitle>
                      <Badge className={getStatusColor(invoice.status)}>
                        {getStatusIcon(invoice.status)}
                        <span className="ml-1">{invoice.status}</span>
                      </Badge>
                      {isOverdue(invoice) && (
                        <Badge variant="destructive">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Overdue
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        Issued: {formatDate(invoice.invoice_date)}
                      </div>
                      {invoice.due_date && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          Due: {formatDate(invoice.due_date)}
                        </div>
                      )}
                      {invoice.paid_at && (
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4" />
                          Paid: {formatDate(invoice.paid_at)}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-lg font-semibold">
                      <DollarSign className="h-5 w-5" />
                      {formatCurrency(invoice.total_amount)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedInvoice(invoice)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Invoice #{invoice.invoice_number}</DialogTitle>
                        </DialogHeader>
                        {selectedInvoice && <InvoiceDetails invoice={selectedInvoice} />}
                      </DialogContent>
                    </Dialog>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => downloadInvoice(invoice)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold mb-2">Invoice Details</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>{formatCurrency(invoice.subtotal)}</span>
                      </div>
                      {invoice.tax_amount > 0 && (
                        <div className="flex justify-between">
                          <span>Tax:</span>
                          <span>{formatCurrency(invoice.tax_amount)}</span>
                        </div>
                      )}
                      {invoice.shipping_amount > 0 && (
                        <div className="flex justify-between">
                          <span>Shipping:</span>
                          <span>{formatCurrency(invoice.shipping_amount)}</span>
                        </div>
                      )}
                      {invoice.discount_amount > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Discount:</span>
                          <span>-{formatCurrency(invoice.discount_amount)}</span>
                        </div>
                      )}
                      <div className="flex justify-between font-semibold border-t pt-1">
                        <span>Total:</span>
                        <span>{formatCurrency(invoice.total_amount)}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Payment Information</h4>
                    <div className="space-y-1 text-sm">
                      {invoice.payment_method && (
                        <div className="flex justify-between">
                          <span>Payment Method:</span>
                          <span>{invoice.payment_method}</span>
                        </div>
                      )}
                      {invoice.payment_reference && (
                        <div className="flex justify-between">
                          <span>Reference:</span>
                          <span className="font-mono">{invoice.payment_reference}</span>
                        </div>
                      )}
                      {invoice.notes && (
                        <div className="mt-2">
                          <span className="font-medium">Notes:</span>
                          <p className="text-muted-foreground mt-1">{invoice.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Line Items Preview */}
                {invoice.invoice_line_items && invoice.invoice_line_items.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Items</h4>
                    <div className="space-y-2">
                      {invoice.invoice_line_items.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex justify-between items-center text-sm">
                          <div>
                            <p className="font-medium">{item.description}</p>
                            <p className="text-muted-foreground">
                              Qty: {item.quantity} × {formatCurrency(item.unit_price)}
                            </p>
                          </div>
                          <p className="font-semibold">{formatCurrency(item.total_price)}</p>
                        </div>
                      ))}
                      {invoice.invoice_line_items.length > 3 && (
                        <p className="text-sm text-muted-foreground">
                          +{invoice.invoice_line_items.length - 3} more items
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No invoices found</h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter !== 'all' 
                  ? 'No invoices match your current filters.' 
                  : 'You don\'t have any invoices yet.'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

// Invoice Details Component
const InvoiceDetails = ({ invoice }: { invoice: Invoice }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Invoice Header */}
      <div className="text-center border-b pb-6">
        <h1 className="text-3xl font-bold">INVOICE</h1>
        <p className="text-xl font-semibold mt-2">#{invoice.invoice_number}</p>
        <div className="flex justify-center items-center gap-2 mt-2">
          <Badge className={invoice.status === 'paid' ? 'bg-green-100 text-green-800' : 
            invoice.status === 'overdue' ? 'bg-red-100 text-red-800' : 
            'bg-yellow-100 text-yellow-800'}>
            {invoice.status.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Invoice Info */}
      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <h3 className="font-semibold mb-3">Invoice Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Invoice Date:</span>
              <span>{formatDate(invoice.invoice_date)}</span>
            </div>
            {invoice.due_date && (
              <div className="flex justify-between">
                <span>Due Date:</span>
                <span>{formatDate(invoice.due_date)}</span>
              </div>
            )}
            {invoice.paid_at && (
              <div className="flex justify-between">
                <span>Paid Date:</span>
                <span>{formatDate(invoice.paid_at)}</span>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-3">Payment Information</h3>
          <div className="space-y-2 text-sm">
            {invoice.payment_method && (
              <div className="flex justify-between">
                <span>Payment Method:</span>
                <span>{invoice.payment_method}</span>
              </div>
            )}
            {invoice.payment_reference && (
              <div className="flex justify-between">
                <span>Reference:</span>
                <span className="font-mono">{invoice.payment_reference}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Line Items */}
      {invoice.invoice_line_items && invoice.invoice_line_items.length > 0 && (
        <div>
          <h3 className="font-semibold mb-4">Invoice Items</h3>
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted">
                <tr>
                  <th className="text-left p-3">Description</th>
                  <th className="text-center p-3">Qty</th>
                  <th className="text-right p-3">Unit Price</th>
                  <th className="text-right p-3">Total</th>
                </tr>
              </thead>
              <tbody>
                {invoice.invoice_line_items.map((item, index) => (
                  <tr key={item.id} className={index % 2 === 0 ? 'bg-background' : 'bg-muted/50'}>
                    <td className="p-3">{item.description}</td>
                    <td className="text-center p-3">{item.quantity}</td>
                    <td className="text-right p-3">{formatCurrency(item.unit_price)}</td>
                    <td className="text-right p-3 font-semibold">{formatCurrency(item.total_price)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Invoice Totals */}
      <div className="flex justify-end">
        <div className="w-full max-w-sm space-y-2">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatCurrency(invoice.subtotal)}</span>
          </div>
          {invoice.tax_amount > 0 && (
            <div className="flex justify-between">
              <span>Tax:</span>
              <span>{formatCurrency(invoice.tax_amount)}</span>
            </div>
          )}
          {invoice.shipping_amount > 0 && (
            <div className="flex justify-between">
              <span>Shipping:</span>
              <span>{formatCurrency(invoice.shipping_amount)}</span>
            </div>
          )}
          {invoice.discount_amount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>Discount:</span>
              <span>-{formatCurrency(invoice.discount_amount)}</span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg border-t pt-2">
            <span>Total:</span>
            <span>{formatCurrency(invoice.total_amount)}</span>
          </div>
        </div>
      </div>

      {/* Notes and Terms */}
      {(invoice.notes || invoice.terms) && (
        <div className="grid gap-4 md:grid-cols-2">
          {invoice.notes && (
            <div>
              <h3 className="font-semibold mb-2">Notes</h3>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm whitespace-pre-wrap">{invoice.notes}</p>
              </div>
            </div>
          )}
          {invoice.terms && (
            <div>
              <h3 className="font-semibold mb-2">Terms & Conditions</h3>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm whitespace-pre-wrap">{invoice.terms}</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
