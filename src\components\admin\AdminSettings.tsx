import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { supabase } from '@/integrations/supabase/client';
import { Save, Settings } from 'lucide-react';
import { toast } from 'sonner';

interface SiteSettings {
  site_name: string;
  site_description: string;
  contact_email: string;
  shipping_enabled: boolean;
  tax_rate: number;
  currency: string;
  admin_email: string;
  order_notifications: boolean;
  inventory_alerts: boolean;
}

export const AdminSettings = () => {
  const [settings, setSettings] = useState<SiteSettings>({
    site_name: '',
    site_description: '',
    contact_email: '',
    shipping_enabled: true,
    tax_rate: 0,
    currency: 'USD',
    admin_email: '',
    order_notifications: true,
    inventory_alerts: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('site_settings')
        .select('key, value');

      if (error) throw error;

      const settingsObj: any = {};
      data?.forEach((setting) => {
        // Handle both JSONB and text values
        let value = setting.value;
        if (typeof value === 'string') {
          // If it's a string, try to parse as JSON, otherwise use as-is
          try {
            value = JSON.parse(value);
          } catch {
            // If parsing fails, remove quotes if present
            if (value.startsWith('"') && value.endsWith('"')) {
              value = value.slice(1, -1);
            }
          }
        }
        settingsObj[setting.key] = value;
      });

      setSettings({
        site_name: settingsObj.site_name || 'Depths of Perception',
        site_description: settingsObj.site_description || 'Custom aquarium decorations',
        contact_email: settingsObj.contact_email || '<EMAIL>',
        shipping_enabled: settingsObj.shipping_enabled === true || settingsObj.shipping_enabled === 'true',
        tax_rate: parseFloat(settingsObj.tax_rate) || 0.08,
        currency: settingsObj.currency || 'USD',
        admin_email: settingsObj.admin_email || '<EMAIL>',
        order_notifications: settingsObj.order_notifications === true || settingsObj.order_notifications === 'true',
        inventory_alerts: settingsObj.inventory_alerts === true || settingsObj.inventory_alerts === 'true',
      });
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      const updates = Object.entries(settings).map(([key, value]) => ({
        key,
        value: JSON.stringify(value), // Always store as JSON string for JSONB compatibility
        description: `Site setting: ${key.replace('_', ' ')}`
      }));

      for (const update of updates) {
        const { error } = await supabase
          .from('site_settings')
          .upsert(update, { onConflict: 'key' });

        if (error) throw error;
      }

      toast.success('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Site Settings</h2>
        <p className="text-muted-foreground">
          Configure your website settings and preferences
        </p>
      </div>

      <div className="grid gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              General Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="site_name">Site Name</Label>
              <Input
                id="site_name"
                value={settings.site_name}
                onChange={(e) => setSettings({ ...settings, site_name: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="site_description">Site Description</Label>
              <Input
                id="site_description"
                value={settings.site_description}
                onChange={(e) => setSettings({ ...settings, site_description: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="contact_email">Contact Email</Label>
              <Input
                id="contact_email"
                type="email"
                value={settings.contact_email}
                onChange={(e) => setSettings({ ...settings, contact_email: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="admin_email">Admin Email</Label>
              <Input
                id="admin_email"
                type="email"
                value={settings.admin_email}
                onChange={(e) => setSettings({ ...settings, admin_email: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>

        {/* E-commerce Settings */}
        <Card>
          <CardHeader>
            <CardTitle>E-commerce Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Input
                  id="currency"
                  value={settings.currency}
                  onChange={(e) => setSettings({ ...settings, currency: e.target.value })}
                  placeholder="USD"
                />
              </div>
              
              <div>
                <Label htmlFor="tax_rate">Tax Rate (%)</Label>
                <Input
                  id="tax_rate"
                  type="number"
                  step="0.01"
                  value={settings.tax_rate}
                  onChange={(e) => setSettings({ ...settings, tax_rate: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="shipping_enabled"
                checked={settings.shipping_enabled}
                onCheckedChange={(checked) => setSettings({ ...settings, shipping_enabled: checked })}
              />
              <Label htmlFor="shipping_enabled">Enable Shipping Calculations</Label>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="order_notifications"
                checked={settings.order_notifications}
                onCheckedChange={(checked) => setSettings({ ...settings, order_notifications: checked })}
              />
              <Label htmlFor="order_notifications">Send Order Notifications</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="inventory_alerts"
                checked={settings.inventory_alerts}
                onCheckedChange={(checked) => setSettings({ ...settings, inventory_alerts: checked })}
              />
              <Label htmlFor="inventory_alerts">Send Inventory Alerts</Label>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSaveSettings} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>
    </div>
  );
};