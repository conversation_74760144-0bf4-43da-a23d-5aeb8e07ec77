import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  ShoppingCart, 
  Package, 
  MessageSquare, 
  FileText, 
  User, 
  CreditCard,
  MapPin,
  Bell,
  Settings,
  Plus,
  Eye,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import { CustomerOrders } from './CustomerOrders';
import { CustomOrderRequests } from './CustomOrderRequests';
import { CustomerInvoices } from './CustomerInvoices';
import { CustomerMessages } from './CustomerMessages';
import { CustomerProfile } from './CustomerProfile';
import { CustomerAddresses } from './CustomerAddresses';
import { CustomerPaymentMethods } from './CustomerPaymentMethods';
import { CustomerNotifications } from './CustomerNotifications';

interface DashboardStats {
  totalOrders: number;
  activeOrders: number;
  customRequests: number;
  unreadMessages: number;
  pendingInvoices: number;
  totalSpent: number;
}

export const CustomerDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    activeOrders: 0,
    customRequests: 0,
    unreadMessages: 0,
    pendingInvoices: 0,
    totalSpent: 0
  });
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Fetch orders
      const { data: orders } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', user.id);

      // Fetch custom requests
      const { data: customRequests } = await supabase
        .from('custom_order_requests')
        .select('*')
        .eq('user_id', user.id);

      // Fetch unread messages
      const { data: messages } = await supabase
        .from('customer_messages')
        .select('*')
        .eq('recipient_id', user.id)
        .eq('is_read', false);

      // Fetch pending invoices
      const { data: invoices } = await supabase
        .from('customer_invoices')
        .select('*')
        .eq('user_id', user.id)
        .in('status', ['sent', 'overdue']);

      // Calculate stats
      const totalOrders = orders?.length || 0;
      const activeOrders = orders?.filter(o => 
        ['pending', 'confirmed', 'processing', 'shipped'].includes(o.status)
      ).length || 0;
      const totalSpent = orders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;

      setStats({
        totalOrders,
        activeOrders,
        customRequests: customRequests?.length || 0,
        unreadMessages: messages?.length || 0,
        pendingInvoices: invoices?.length || 0,
        totalSpent
      });

      // Fetch recent activity
      const recentOrders = orders?.slice(0, 3) || [];
      const recentRequests = customRequests?.slice(0, 2) || [];
      const recentMessages = messages?.slice(0, 2) || [];

      const activity = [
        ...recentOrders.map(order => ({
          type: 'order',
          title: `Order #${order.order_number}`,
          description: `Status: ${order.status}`,
          date: order.created_at,
          status: order.status
        })),
        ...recentRequests.map(request => ({
          type: 'custom_request',
          title: request.title,
          description: `Status: ${request.status}`,
          date: request.created_at,
          status: request.status
        })),
        ...recentMessages.map(message => ({
          type: 'message',
          title: message.subject || 'New Message',
          description: message.message.substring(0, 100) + '...',
          date: message.created_at,
          status: 'unread'
        }))
      ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5);

      setRecentActivity(activity);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      pending: 'bg-yellow-100 text-yellow-800',
      confirmed: 'bg-blue-100 text-blue-800',
      processing: 'bg-purple-100 text-purple-800',
      shipped: 'bg-indigo-100 text-indigo-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
      submitted: 'bg-blue-100 text-blue-800',
      under_review: 'bg-yellow-100 text-yellow-800',
      quote_provided: 'bg-purple-100 text-purple-800',
      approved: 'bg-green-100 text-green-800',
      in_progress: 'bg-indigo-100 text-indigo-800',
      completed: 'bg-green-100 text-green-800',
      unread: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-8">Loading your dashboard...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight">My Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            Manage your orders, custom requests, and account settings
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-8 mb-8">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="orders" className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4" />
              Orders
            </TabsTrigger>
            <TabsTrigger value="custom-requests" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Custom Requests
            </TabsTrigger>
            <TabsTrigger value="invoices" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Invoices
            </TabsTrigger>
            <TabsTrigger value="messages" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Messages
              {stats.unreadMessages > 0 && (
                <Badge variant="destructive" className="ml-1 text-xs">
                  {stats.unreadMessages}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="addresses" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Addresses
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment Methods
            </TabsTrigger>
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalOrders}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.activeOrders} active orders
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Custom Requests</CardTitle>
                  <Plus className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.customRequests}</div>
                  <p className="text-xs text-muted-foreground">
                    Custom aquarium projects
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(stats.totalSpent)}</div>
                  <p className="text-xs text-muted-foreground">
                    Lifetime purchases
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Unread Messages</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.unreadMessages}</div>
                  <p className="text-xs text-muted-foreground">
                    New communications
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.pendingInvoices}</div>
                  <p className="text-xs text-muted-foreground">
                    Awaiting payment
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.length > 0 ? (
                    recentActivity.map((activity, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            {activity.type === 'order' && <ShoppingCart className="h-5 w-5 text-blue-600" />}
                            {activity.type === 'custom_request' && <Plus className="h-5 w-5 text-purple-600" />}
                            {activity.type === 'message' && <MessageSquare className="h-5 w-5 text-green-600" />}
                          </div>
                          <div>
                            <p className="font-medium">{activity.title}</p>
                            <p className="text-sm text-muted-foreground">{activity.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getStatusColor(activity.status)}>
                            {activity.status.replace('_', ' ')}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {new Date(activity.date).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No recent activity
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Button 
                    onClick={() => setActiveTab('custom-requests')}
                    className="h-20 flex flex-col items-center justify-center space-y-2"
                  >
                    <Plus className="h-6 w-6" />
                    <span>New Custom Request</span>
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setActiveTab('orders')}
                    className="h-20 flex flex-col items-center justify-center space-y-2"
                  >
                    <Eye className="h-6 w-6" />
                    <span>View Orders</span>
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setActiveTab('messages')}
                    className="h-20 flex flex-col items-center justify-center space-y-2"
                  >
                    <MessageSquare className="h-6 w-6" />
                    <span>Send Message</span>
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setActiveTab('profile')}
                    className="h-20 flex flex-col items-center justify-center space-y-2"
                  >
                    <Settings className="h-6 w-6" />
                    <span>Account Settings</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders">
            <CustomerOrders />
          </TabsContent>

          <TabsContent value="custom-requests">
            <CustomOrderRequests />
          </TabsContent>

          <TabsContent value="invoices">
            <CustomerInvoices />
          </TabsContent>

          <TabsContent value="messages">
            <CustomerMessages />
          </TabsContent>

          <TabsContent value="addresses">
            <CustomerAddresses />
          </TabsContent>

          <TabsContent value="payments">
            <CustomerPaymentMethods />
          </TabsContent>

          <TabsContent value="profile">
            <CustomerProfile />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
